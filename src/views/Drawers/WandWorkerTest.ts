/**
 * 测试文件：验证Wand Worker实现
 */

// 简单的测试函数来验证Worker是否正常工作
export function testWandWorker() {
  console.log('Testing Wand Worker implementation...');
  
  // 创建一个简单的测试图像数据
  const testImageData = {
    data: new Uint8ClampedArray(100 * 100 * 4), // 100x100 像素
    width: 100,
    height: 100
  };
  
  // 填充一些测试数据 - 创建一个简单的图案
  for (let y = 0; y < 100; y++) {
    for (let x = 0; x < 100; x++) {
      const index = (y * 100 + x) * 4;
      
      // 创建一个简单的渐变图案
      if (x < 50 && y < 50) {
        // 左上角 - 红色区域
        testImageData.data[index] = 255;     // R
        testImageData.data[index + 1] = 0;   // G
        testImageData.data[index + 2] = 0;   // B
        testImageData.data[index + 3] = 255; // A
      } else if (x >= 50 && y < 50) {
        // 右上角 - 绿色区域
        testImageData.data[index] = 0;       // R
        testImageData.data[index + 1] = 255; // G
        testImageData.data[index + 2] = 0;   // B
        testImageData.data[index + 3] = 255; // A
      } else if (x < 50 && y >= 50) {
        // 左下角 - 蓝色区域
        testImageData.data[index] = 0;       // R
        testImageData.data[index + 1] = 0;   // G
        testImageData.data[index + 2] = 255; // B
        testImageData.data[index + 3] = 255; // A
      } else {
        // 右下角 - 白色区域
        testImageData.data[index] = 255;     // R
        testImageData.data[index + 1] = 255; // G
        testImageData.data[index + 2] = 255; // B
        testImageData.data[index + 3] = 255; // A
      }
    }
  }
  
  console.log('Test image data created:', {
    width: testImageData.width,
    height: testImageData.height,
    dataLength: testImageData.data.length
  });
  
  return testImageData;
}

// 测试参数
export const testParams = {
  bufferCenter: 50,
  bufferSize: 100,
  sensitivity: 20,
  maxRegionPixels: 1000,
  mode: 'rgb'
};

console.log('WandWorkerTest module loaded');
